package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/cainuro/orchestrator/internal/config"
	"github.com/cainuro/orchestrator/internal/server"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"google.golang.org/grpc"
)

var (
	configPath = flag.String("config", "/etc/cainuro/orchestrator-config.yaml", "Path to configuration file")
	version    = flag.Bool("version", false, "Show version information")
	help       = flag.Bool("help", false, "Show help information")
)

const (
	AppName    = "cai-orchestrator"
	AppVersion = "1.0.0"
)

func main() {
	flag.Parse()

	if *help {
		fmt.Printf("%s - CAINuro Orchestrator\n", AppName)
		fmt.Printf("Version: %s\n\n", AppVersion)
		fmt.Println("Usage:")
		flag.PrintDefaults()
		os.Exit(0)
	}

	if *version {
		fmt.Printf("%s %s\n", AppName, AppVersion)
		os.Exit(0)
	}

	// Load configuration
	cfg, err := config.Load(*configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize server components
	srv, cleanup, err := server.InitializeServer(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize server: %v", err)
	}
	defer cleanup()

	// Setup graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start gRPC server
	grpcServer := grpc.NewServer()
	srv.RegisterGRPCServices(grpcServer)

	grpcListener, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Server.GRPCPort))
	if err != nil {
		log.Fatalf("Failed to listen on gRPC port %d: %v", cfg.Server.GRPCPort, err)
	}

	go func() {
		log.Printf("Starting gRPC server on port %d", cfg.Server.GRPCPort)
		if err := grpcServer.Serve(grpcListener); err != nil {
			log.Printf("gRPC server error: %v", err)
		}
	}()

	// Start HTTP server with embedded UI
	app := fiber.New(fiber.Config{
		AppName:      AppName,
		ServerHeader: fmt.Sprintf("%s/%s", AppName, AppVersion),
	})

	// Middleware
	app.Use(recover.New())
	app.Use(logger.New())
	app.Use(cors.New())

	// Register HTTP routes
	srv.RegisterHTTPRoutes(app)

	go func() {
		log.Printf("Starting HTTP server on port %d", cfg.Server.HTTPPort)
		if err := app.Listen(fmt.Sprintf(":%d", cfg.Server.HTTPPort)); err != nil {
			log.Printf("HTTP server error: %v", err)
		}
	}()

	// Start Envoy xDS server
	if cfg.Envoy.XDSPort > 0 {
		go func() {
			log.Printf("Starting Envoy xDS server on port %d", cfg.Envoy.XDSPort)
			if err := srv.StartEnvoyXDS(ctx, cfg.Envoy.XDSPort); err != nil {
				log.Printf("Envoy xDS server error: %v", err)
			}
		}()
	}

	// Wait for shutdown signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	log.Println("Shutting down gracefully...")

	// Shutdown servers
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	grpcServer.GracefulStop()
	
	if err := app.ShutdownWithContext(shutdownCtx); err != nil {
		log.Printf("HTTP server shutdown error: %v", err)
	}

	log.Println("Server stopped")
}
