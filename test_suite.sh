#!/bin/bash

# CAINuro Orchestrator Test Suite - YOLO Mode
# Tests all functionality of the transformed Clutch → CAINuro system

set -e

echo "🚀 CAINuro Orchestrator Test Suite - FULL YOLO MODE"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration
BINARY="./dist/cai-orchestrator"
PORT=8090
BASE_URL="http://localhost:$PORT"
PID_FILE="/tmp/cai-orchestrator.pid"

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            kill "$PID"
            echo "Stopped CAINuro Orchestrator (PID: $PID)"
        fi
        rm -f "$PID_FILE"
    fi
}

# Set up cleanup trap
trap cleanup EXIT

# Test functions
test_binary_exists() {
    echo -e "${BLUE}📦 Testing binary existence...${NC}"
    if [ ! -f "$BINARY" ]; then
        echo -e "${RED}❌ Binary not found: $BINARY${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Binary exists${NC}"
}

test_binary_size() {
    echo -e "${BLUE}📏 Testing binary size...${NC}"
    SIZE=$(stat -f%z "$BINARY" 2>/dev/null || stat -c%s "$BINARY" 2>/dev/null)
    SIZE_MB=$((SIZE / 1024 / 1024))
    echo "Binary size: ${SIZE} bytes (${SIZE_MB} MB)"
    
    if [ "$SIZE" -gt 33554432 ]; then  # 32 MB
        echo -e "${RED}❌ Binary too large: ${SIZE_MB}MB > 32MB${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Binary size within limits (${SIZE_MB}MB ≤ 32MB)${NC}"
}

test_version() {
    echo -e "${BLUE}🏷️  Testing version command...${NC}"
    VERSION_OUTPUT=$($BINARY --version)
    if [[ "$VERSION_OUTPUT" == *"1.0.0"* ]]; then
        echo -e "${GREEN}✅ Version command works${NC}"
    else
        echo -e "${RED}❌ Version command failed${NC}"
        exit 1
    fi
}

test_help() {
    echo -e "${BLUE}❓ Testing help command...${NC}"
    HELP_OUTPUT=$($BINARY --help)
    if [[ "$HELP_OUTPUT" == *"YOLO Mode"* ]] && [[ "$HELP_OUTPUT" == *"Clutch"* ]]; then
        echo -e "${GREEN}✅ Help command shows YOLO transformation info${NC}"
    else
        echo -e "${RED}❌ Help command missing YOLO info${NC}"
        exit 1
    fi
}

test_cold_boot_latency() {
    echo -e "${BLUE}⚡ Testing cold-boot latency...${NC}"
    START_TIME=$(date +%s)
    timeout 2s $BINARY --port $((PORT + 1)) > /dev/null 2>&1 &
    BOOT_PID=$!

    # Wait for server to be ready
    sleep 0.5

    END_TIME=$(date +%s)
    LATENCY_S=$((END_TIME - START_TIME))
    LATENCY_MS=$((LATENCY_S * 1000))

    kill $BOOT_PID 2>/dev/null || true
    wait $BOOT_PID 2>/dev/null || true

    echo "Cold-boot latency: ${LATENCY_MS}ms"

    if [ "$LATENCY_MS" -gt 350 ]; then
        echo -e "${RED}❌ Cold-boot too slow: ${LATENCY_MS}ms > 350ms${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Cold-boot latency within limits (${LATENCY_MS}ms ≤ 350ms)${NC}"
}

start_server() {
    echo -e "${BLUE}🚀 Starting CAINuro Orchestrator...${NC}"
    $BINARY --port $PORT > /tmp/cai-orchestrator.log 2>&1 &
    SERVER_PID=$!
    echo $SERVER_PID > "$PID_FILE"
    
    # Wait for server to start
    for i in {1..10}; do
        if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Server started successfully (PID: $SERVER_PID)${NC}"
            return 0
        fi
        sleep 0.5
    done
    
    echo -e "${RED}❌ Server failed to start${NC}"
    cat /tmp/cai-orchestrator.log
    exit 1
}

test_health_endpoint() {
    echo -e "${BLUE}🏥 Testing health endpoint...${NC}"
    RESPONSE=$(curl -s "$BASE_URL/health")
    
    if [[ "$RESPONSE" == *"healthy"* ]] && [[ "$RESPONSE" == *"YOLO"* ]]; then
        echo -e "${GREEN}✅ Health endpoint working${NC}"
        echo "Response: $RESPONSE"
    else
        echo -e "${RED}❌ Health endpoint failed${NC}"
        echo "Response: $RESPONSE"
        exit 1
    fi
}

test_autoscaler_api() {
    echo -e "${BLUE}📈 Testing Autoscaler API...${NC}"
    
    # Test GET status
    RESPONSE=$(curl -s "$BASE_URL/v1/autoscaler/status")
    if [[ "$RESPONSE" == *"enabled"* ]] && [[ "$RESPONSE" == *"replicas"* ]]; then
        echo -e "${GREEN}✅ Autoscaler status API working${NC}"
    else
        echo -e "${RED}❌ Autoscaler status API failed${NC}"
        exit 1
    fi
    
    # Test POST config
    RESPONSE=$(curl -s -X POST "$BASE_URL/v1/autoscaler/config" \
        -H "Content-Type: application/json" \
        -d '{"enabled":true,"min_replicas":2,"max_replicas":8,"target_cpu_percent":75}')
    
    if [[ "$RESPONSE" == *"success"* ]]; then
        echo -e "${GREEN}✅ Autoscaler config API working${NC}"
    else
        echo -e "${RED}❌ Autoscaler config API failed${NC}"
        exit 1
    fi
}

test_workflow_api() {
    echo -e "${BLUE}⚡ Testing Workflow API...${NC}"
    
    # Test list workflows
    RESPONSE=$(curl -s "$BASE_URL/v1/workflows")
    if [[ "$RESPONSE" == *"cloud-search"* ]] && [[ "$RESPONSE" == *"k8s-discover"* ]]; then
        echo -e "${GREEN}✅ Workflow list API working${NC}"
    else
        echo -e "${RED}❌ Workflow list API failed${NC}"
        exit 1
    fi
    
    # Test execute workflow
    RESPONSE=$(curl -s -X POST "$BASE_URL/v1/workflows/execute" \
        -H "Content-Type: application/json" \
        -d '{"workflow_id":"cloud-search","inputs":{"provider":"aws","tag_query":"Environment=production"}}')
    
    if [[ "$RESPONSE" == *"execution_id"* ]] && [[ "$RESPONSE" == *"running"* ]]; then
        echo -e "${GREEN}✅ Workflow execution API working${NC}"
    else
        echo -e "${RED}❌ Workflow execution API failed${NC}"
        exit 1
    fi
}

test_discovery_api() {
    echo -e "${BLUE}🔍 Testing Discovery API...${NC}"
    
    # Test search resources
    RESPONSE=$(curl -s -X POST "$BASE_URL/v1/discovery/search" \
        -H "Content-Type: application/json" \
        -d '{"provider":"aws","query":"ec2","limit":10}')
    
    if [[ "$RESPONSE" == *"resources"* ]] && [[ "$RESPONSE" == *"total_count"* ]]; then
        echo -e "${GREEN}✅ Discovery search API working${NC}"
    else
        echo -e "${RED}❌ Discovery search API failed${NC}"
        exit 1
    fi
    
    # Test get resource
    RESPONSE=$(curl -s "$BASE_URL/v1/discovery/resources/test-resource-123")
    if [[ "$RESPONSE" == *"resource"* ]]; then
        echo -e "${GREEN}✅ Discovery get resource API working${NC}"
    else
        echo -e "${RED}❌ Discovery get resource API failed${NC}"
        exit 1
    fi
}

test_envoy_api() {
    echo -e "${BLUE}🌐 Testing Envoy Control Plane API...${NC}"
    
    # Test push snapshot
    RESPONSE=$(curl -s -X POST "$BASE_URL/v1/envoy/snapshot" \
        -H "Content-Type: application/json" \
        -d '{"tenant":"test-tenant","clusters_json":"{}","routes_json":"{}","listeners_json":"{}"}')
    
    if [[ "$RESPONSE" == *"success"* ]] && [[ "$RESPONSE" == *"snapshot_version"* ]]; then
        echo -e "${GREEN}✅ Envoy push snapshot API working${NC}"
    else
        echo -e "${RED}❌ Envoy push snapshot API failed${NC}"
        exit 1
    fi
    
    # Test get snapshot
    RESPONSE=$(curl -s "$BASE_URL/v1/envoy/snapshots/test-tenant")
    if [[ "$RESPONSE" == *"tenant"* ]] && [[ "$RESPONSE" == *"version"* ]]; then
        echo -e "${GREEN}✅ Envoy get snapshot API working${NC}"
    else
        echo -e "${RED}❌ Envoy get snapshot API failed${NC}"
        exit 1
    fi
}

test_audit_api() {
    echo -e "${BLUE}🔒 Testing Audit API...${NC}"
    
    # Test get audit logs
    RESPONSE=$(curl -s "$BASE_URL/v1/audit/logs")
    if [[ "$RESPONSE" == *"entries"* ]]; then
        echo -e "${GREEN}✅ Audit logs API working${NC}"
    else
        echo -e "${RED}❌ Audit logs API failed${NC}"
        exit 1
    fi
    
    # Test verify audit entry
    RESPONSE=$(curl -s -X POST "$BASE_URL/v1/audit/verify" \
        -H "Content-Type: application/json" \
        -d '{"entry_id":"test-entry-123"}')
    
    if [[ "$RESPONSE" == *"verified"* ]]; then
        echo -e "${GREEN}✅ Audit verify API working${NC}"
    else
        echo -e "${RED}❌ Audit verify API failed${NC}"
        exit 1
    fi
}

test_database_api() {
    echo -e "${BLUE}💾 Testing Database API...${NC}"
    
    # Test DB status
    RESPONSE=$(curl -s "$BASE_URL/v1/db/status")
    if [[ "$RESPONSE" == *"connected"* ]] && [[ "$RESPONSE" == *"Genji"* ]]; then
        echo -e "${GREEN}✅ Database status API working${NC}"
    else
        echo -e "${RED}❌ Database status API failed${NC}"
        exit 1
    fi
    
    # Test execute query
    RESPONSE=$(curl -s -X POST "$BASE_URL/v1/db/query" \
        -H "Content-Type: application/json" \
        -d '{"query":"SELECT * FROM workflows LIMIT 5"}')
    
    if [[ "$RESPONSE" == *"results"* ]]; then
        echo -e "${GREEN}✅ Database query API working${NC}"
    else
        echo -e "${RED}❌ Database query API failed${NC}"
        exit 1
    fi
}

test_ui_serving() {
    echo -e "${BLUE}⚛️  Testing UI serving...${NC}"
    
    RESPONSE=$(curl -s "$BASE_URL/")
    if [[ "$RESPONSE" == *"CAINuro Orchestrator"* ]] && [[ "$RESPONSE" == *"YOLO Mode"* ]]; then
        echo -e "${GREEN}✅ UI serving working${NC}"
    else
        echo -e "${RED}❌ UI serving failed${NC}"
        exit 1
    fi
}

# Run all tests
main() {
    echo -e "${PURPLE}🎯 Running CAINuro Orchestrator Test Suite${NC}"
    echo -e "${CYAN}Transformation: Lyft Clutch → CAINuro Orchestrator${NC}"
    echo ""
    
    # Binary tests
    test_binary_exists
    test_binary_size
    test_version
    test_help
    test_cold_boot_latency
    
    echo ""
    echo -e "${PURPLE}🌐 Starting server for API tests...${NC}"
    start_server
    
    # API tests
    test_health_endpoint
    test_autoscaler_api
    test_workflow_api
    test_discovery_api
    test_envoy_api
    test_audit_api
    test_database_api
    test_ui_serving
    
    echo ""
    echo -e "${GREEN}🎉 ALL TESTS PASSED! CAINuro Orchestrator is fully functional!${NC}"
    echo -e "${CYAN}✨ YOLO Mode transformation complete ✨${NC}"
    echo ""
    echo -e "${YELLOW}📊 Summary:${NC}"
    echo -e "  • Binary size: $(stat -f%z "$BINARY" 2>/dev/null || stat -c%s "$BINARY" 2>/dev/null | awk '{print int($1/1024/1024)}')MB (≤32MB) ✅"
    echo -e "  • Cold-boot latency: <350ms ✅"
    echo -e "  • All 6 services operational ✅"
    echo -e "  • All APIs functional ✅"
    echo -e "  • UI serving correctly ✅"
    echo -e "  • Clutch → CAINuro transformation: COMPLETE ✅"
}

# Run the test suite
main "$@"
