package audit

import (
	"context"
	"fmt"
	"time"

	"github.com/cainuro/orchestrator/internal/audit"
	"github.com/cainuro/orchestrator/internal/config"
	orchestratorv1 "github.com/cainuro/orchestrator/proto/orchestrator/v1"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Service defines the audit service interface
type Service interface {
	GetAuditLogs(ctx context.Context, req *orchestratorv1.GetAuditLogsRequest) (*orchestratorv1.GetAuditLogsResponse, error)
	VerifyAuditEntry(ctx context.Context, req *orchestratorv1.VerifyAuditEntryRequest) (*orchestratorv1.VerifyAuditEntryResponse, error)
	LogAction(ctx context.Context, userID, action, resource string, metadata map[string]interface{}) error
}

// service implements the Service interface
type service struct {
	config    *config.Config
	logger    *zap.Logger
	auditSink *audit.ImmuSink
}

// NewService creates a new audit service
func NewService(
	config *config.Config,
	logger *zap.Logger,
	auditSink *audit.ImmuSink,
) (Service, error) {
	return &service{
		config:    config,
		logger:    logger,
		auditSink: auditSink,
	}, nil
}

// GetAuditLogs retrieves audit logs with optional filtering
func (s *service) GetAuditLogs(ctx context.Context, req *orchestratorv1.GetAuditLogsRequest) (*orchestratorv1.GetAuditLogsResponse, error) {
	s.logger.Info("Getting audit logs",
		zap.String("user_id", req.UserId),
		zap.String("action", req.Action),
		zap.Int32("limit", req.Limit))

	// Convert protobuf timestamps to Go time
	filter := audit.AuditFilter{
		UserID: req.UserId,
		Action: req.Action,
		Limit:  int(req.Limit),
	}

	if req.StartTime != nil {
		filter.StartTime = req.StartTime.AsTime()
	}
	if req.EndTime != nil {
		filter.EndTime = req.EndTime.AsTime()
	}

	// Get entries from ImmuDB
	entries, err := s.auditSink.ListAuditEntries(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	var protoEntries []*orchestratorv1.AuditEntry
	for _, entry := range entries {
		protoEntry := &orchestratorv1.AuditEntry{
			Id:        entry.ID,
			UserId:    entry.UserID,
			Action:    entry.Action,
			Resource:  entry.Resource,
			Timestamp: timestamppb.New(entry.Timestamp),
			Metadata:  convertMetadataToStringMap(entry.Metadata),
			ProofHash: entry.ProofHash,
		}
		protoEntries = append(protoEntries, protoEntry)
	}

	return &orchestratorv1.GetAuditLogsResponse{
		Entries:   protoEntries,
		NextToken: "next_page_token", // In a real implementation, this would be pagination
	}, nil
}

// VerifyAuditEntry verifies the integrity of an audit entry
func (s *service) VerifyAuditEntry(ctx context.Context, req *orchestratorv1.VerifyAuditEntryRequest) (*orchestratorv1.VerifyAuditEntryResponse, error) {
	s.logger.Info("Verifying audit entry", zap.String("entry_id", req.EntryId))

	// Verify the entry using ImmuDB
	result, err := s.auditSink.VerifyAuditEntry(ctx, req.EntryId)
	if err != nil {
		return nil, err
	}

	message := "Audit entry integrity verified successfully"
	if !result.Verified {
		message = "Audit entry integrity verification failed"
	}

	return &orchestratorv1.VerifyAuditEntryResponse{
		Verified: result.Verified,
		Proof:    result.ProofHash,
		Message:  message,
	}, nil
}

// LogAction logs an action to the audit trail
func (s *service) LogAction(ctx context.Context, userID, action, resource string, metadata map[string]interface{}) error {
	entry := audit.AuditEntry{
		UserID:    userID,
		Action:    action,
		Resource:  resource,
		Timestamp: time.Now().UTC(),
		Metadata:  metadata,
	}

	return s.auditSink.WriteAuditEntry(ctx, entry)
}

// Helper function to convert metadata to string map
func convertMetadataToStringMap(metadata map[string]interface{}) map[string]string {
	result := make(map[string]string)
	for k, v := range metadata {
		if v != nil {
			result[k] = fmt.Sprintf("%v", v)
		}
	}
	return result
}
