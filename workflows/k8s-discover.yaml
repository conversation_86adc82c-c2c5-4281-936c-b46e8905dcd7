id: k8s-discover
name: Kubernetes Discovery
description: Discover Kubernetes clusters, pods, services, and workloads across environments

inputs:
  clusters:
    type: array
    description: Kubernetes cluster contexts to scan
    default: ["prod-cluster", "staging-cluster"]
  namespaces:
    type: array
    description: Namespaces to scan (empty = all)
    default: []
  resource_types:
    type: array
    description: K8s resource types to discover
    default: ["pods", "services", "deployments", "configmaps", "secrets"]
  include_system:
    type: boolean
    description: Include system namespaces (kube-system, etc.)
    default: false

outputs:
  clusters_scanned:
    type: integer
    description: Number of clusters successfully scanned
  total_resources:
    type: integer
    description: Total resources discovered
  resource_breakdown:
    type: object
    description: Resource counts by type and namespace
  health_report:
    type: object
    description: Cluster health summary

steps:
  - id: validate_clusters
    type: k8s.validate_access
    description: Validate access to all specified clusters
    with:
      clusters: "{{inputs.clusters}}"
    
  - id: discover_cluster_info
    type: k8s.cluster_info
    description: Get cluster information and node status
    depends_on: [validate_clusters]
    with:
      clusters: "{{inputs.clusters}}"
    
  - id: list_namespaces
    type: k8s.list_namespaces
    description: List all namespaces in clusters
    depends_on: [validate_clusters]
    with:
      clusters: "{{inputs.clusters}}"
      include_system: "{{inputs.include_system}}"
    
  - id: discover_pods
    type: k8s.list_pods
    description: Discover all pods
    depends_on: [list_namespaces]
    condition: "{{contains(inputs.resource_types, 'pods')}}"
    with:
      clusters: "{{inputs.clusters}}"
      namespaces: "{{inputs.namespaces || steps.list_namespaces.outputs.namespaces}}"
    
  - id: discover_services
    type: k8s.list_services
    description: Discover all services
    depends_on: [list_namespaces]
    condition: "{{contains(inputs.resource_types, 'services')}}"
    with:
      clusters: "{{inputs.clusters}}"
      namespaces: "{{inputs.namespaces || steps.list_namespaces.outputs.namespaces}}"
    
  - id: discover_deployments
    type: k8s.list_deployments
    description: Discover all deployments
    depends_on: [list_namespaces]
    condition: "{{contains(inputs.resource_types, 'deployments')}}"
    with:
      clusters: "{{inputs.clusters}}"
      namespaces: "{{inputs.namespaces || steps.list_namespaces.outputs.namespaces}}"
    
  - id: discover_configmaps
    type: k8s.list_configmaps
    description: Discover all configmaps
    depends_on: [list_namespaces]
    condition: "{{contains(inputs.resource_types, 'configmaps')}}"
    with:
      clusters: "{{inputs.clusters}}"
      namespaces: "{{inputs.namespaces || steps.list_namespaces.outputs.namespaces}}"
    
  - id: discover_secrets
    type: k8s.list_secrets
    description: Discover all secrets (metadata only)
    depends_on: [list_namespaces]
    condition: "{{contains(inputs.resource_types, 'secrets')}}"
    with:
      clusters: "{{inputs.clusters}}"
      namespaces: "{{inputs.namespaces || steps.list_namespaces.outputs.namespaces}}"
      metadata_only: true
    
  - id: analyze_health
    type: k8s.health_check
    description: Analyze cluster and workload health
    depends_on: [discover_pods, discover_services, discover_deployments]
    with:
      pods: "{{steps.discover_pods.outputs.pods}}"
      services: "{{steps.discover_services.outputs.services}}"
      deployments: "{{steps.discover_deployments.outputs.deployments}}"
    
  - id: generate_topology
    type: k8s.topology_map
    description: Generate service topology map
    depends_on: [discover_services, discover_pods]
    with:
      services: "{{steps.discover_services.outputs.services}}"
      pods: "{{steps.discover_pods.outputs.pods}}"
    
  - id: security_scan
    type: k8s.security_scan
    description: Basic security posture scan
    depends_on: [discover_pods, discover_secrets]
    with:
      pods: "{{steps.discover_pods.outputs.pods}}"
      secrets: "{{steps.discover_secrets.outputs.secrets}}"
      check_rbac: true
      check_network_policies: true
    
  - id: resource_utilization
    type: k8s.resource_usage
    description: Analyze resource utilization
    depends_on: [discover_pods, discover_cluster_info]
    with:
      pods: "{{steps.discover_pods.outputs.pods}}"
      cluster_info: "{{steps.discover_cluster_info.outputs.clusters}}"
    
  - id: export_inventory
    type: workflow.export.json
    description: Export complete inventory
    depends_on: [analyze_health, generate_topology, security_scan, resource_utilization]
    with:
      filename: "k8s_inventory_{{timestamp}}.json"
      data:
        clusters: "{{steps.discover_cluster_info.outputs.clusters}}"
        namespaces: "{{steps.list_namespaces.outputs.namespaces}}"
        pods: "{{steps.discover_pods.outputs.pods}}"
        services: "{{steps.discover_services.outputs.services}}"
        deployments: "{{steps.discover_deployments.outputs.deployments}}"
        configmaps: "{{steps.discover_configmaps.outputs.configmaps}}"
        secrets: "{{steps.discover_secrets.outputs.secrets}}"
        health: "{{steps.analyze_health.outputs.health_report}}"
        topology: "{{steps.generate_topology.outputs.topology}}"
        security: "{{steps.security_scan.outputs.security_report}}"
        utilization: "{{steps.resource_utilization.outputs.utilization_report}}"
    
  - id: update_cache
    type: workflow.cache
    description: Update discovery cache
    depends_on: [export_inventory]
    with:
      key: "k8s_discovery_{{hash(inputs.clusters)}}"
      data: "{{steps.export_inventory.outputs.data}}"
      ttl: "30m"

error_handling:
  retry_policy:
    max_attempts: 2
    backoff: linear
    base_delay: 2s
  
  on_failure:
    - type: audit.log
      with:
        action: "k8s.discovery.failed"
        workflow_id: "{{workflow.id}}"
        clusters: "{{inputs.clusters}}"
        error: "{{error.message}}"

timeout: 15m

metadata:
  version: "1.0.0"
  author: "CAINuro Orchestrator"
  tags: ["kubernetes", "discovery", "inventory"]
  created_at: "2024-01-01T00:00:00Z"
