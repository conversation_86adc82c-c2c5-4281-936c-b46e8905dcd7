id: cloud-search
name: Cloud Resource Search
description: Search for cloud resources across providers with advanced filtering

inputs:
  provider:
    type: string
    description: Cloud provider (aws|gcp|azure|all)
    default: "all"
  tag_query:
    type: string
    description: Tag-based query filter
    default: "Environment=production"
  resource_types:
    type: array
    description: Resource types to search for
    default: ["ec2.instance", "compute.instance", "virtualMachines"]
  regions:
    type: array
    description: Regions to search in
    default: ["us-east-1", "us-west-2", "us-central1-a"]

outputs:
  resources_found:
    type: integer
    description: Number of resources discovered
  csv_file:
    type: string
    description: Path to exported CSV file
  summary_report:
    type: object
    description: Summary statistics by provider

steps:
  - id: validate_inputs
    type: workflow.validate
    description: Validate input parameters
    with:
      provider: "{{inputs.provider}}"
      tag_query: "{{inputs.tag_query}}"
    
  - id: fetch_aws_resources
    type: discovery.aws.search
    description: Search AWS resources
    depends_on: [validate_inputs]
    condition: "{{inputs.provider == 'aws' || inputs.provider == 'all'}}"
    with:
      regions: "{{inputs.regions}}"
      tag_query: "{{inputs.tag_query}}"
      resource_types: "{{inputs.resource_types}}"
    
  - id: fetch_gcp_resources
    type: discovery.gcp.search
    description: Search GCP resources
    depends_on: [validate_inputs]
    condition: "{{inputs.provider == 'gcp' || inputs.provider == 'all'}}"
    with:
      projects: ["{{env.GCP_PROJECT}}"]
      tag_query: "{{inputs.tag_query}}"
      resource_types: "{{inputs.resource_types}}"
    
  - id: fetch_azure_resources
    type: discovery.azure.search
    description: Search Azure resources
    depends_on: [validate_inputs]
    condition: "{{inputs.provider == 'azure' || inputs.provider == 'all'}}"
    with:
      subscriptions: ["{{env.AZURE_SUBSCRIPTION}}"]
      tag_query: "{{inputs.tag_query}}"
      resource_types: "{{inputs.resource_types}}"
    
  - id: merge_results
    type: workflow.merge
    description: Merge results from all providers
    depends_on: [fetch_aws_resources, fetch_gcp_resources, fetch_azure_resources]
    with:
      aws_results: "{{steps.fetch_aws_resources.outputs.resources}}"
      gcp_results: "{{steps.fetch_gcp_resources.outputs.resources}}"
      azure_results: "{{steps.fetch_azure_resources.outputs.resources}}"
    
  - id: generate_summary
    type: workflow.summarize
    description: Generate summary statistics
    depends_on: [merge_results]
    with:
      resources: "{{steps.merge_results.outputs.merged_resources}}"
    
  - id: export_csv
    type: workflow.export.csv
    description: Export results to CSV
    depends_on: [merge_results]
    with:
      resources: "{{steps.merge_results.outputs.merged_resources}}"
      filename: "cloud_resources_{{timestamp}}.csv"
      columns: ["provider", "type", "name", "id", "region", "tags"]
    
  - id: cache_results
    type: workflow.cache
    description: Cache results for future queries
    depends_on: [merge_results]
    with:
      key: "search_{{inputs.provider}}_{{hash(inputs.tag_query)}}"
      data: "{{steps.merge_results.outputs.merged_resources}}"
      ttl: "1h"

error_handling:
  retry_policy:
    max_attempts: 3
    backoff: exponential
    base_delay: 1s
  
  on_failure:
    - type: audit.log
      with:
        action: "workflow.failed"
        workflow_id: "{{workflow.id}}"
        error: "{{error.message}}"
    
    - type: notification.slack
      condition: "{{env.SLACK_WEBHOOK_URL != ''}}"
      with:
        webhook_url: "{{env.SLACK_WEBHOOK_URL}}"
        message: "Cloud search workflow failed: {{error.message}}"

timeout: 10m

metadata:
  version: "1.0.0"
  author: "CAINuro Orchestrator"
  tags: ["discovery", "cloud", "search"]
  created_at: "2024-01-01T00:00:00Z"
