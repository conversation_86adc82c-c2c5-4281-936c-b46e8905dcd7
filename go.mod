module github.com/cainuro/orchestrator

go 1.24

require (
	github.com/genjidb/genji v0.16.0
	github.com/dgraph-io/ristretto v0.1.1
	github.com/codenotary/immudb v1.5.0
	github.com/google/wire v0.5.0
	github.com/spf13/viper v1.17.0
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.18.1
	google.golang.org/grpc v1.59.0
	google.golang.org/protobuf v1.31.0
	github.com/aws/aws-sdk-go-v2 v1.21.2
	github.com/aws/aws-sdk-go-v2/service/resourcegroupstaggingapi v1.15.24
	cloud.google.com/go/asset v1.15.3
	github.com/Azure/azure-sdk-for-go/sdk/azcore v1.8.0
	github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/resources/armresources v1.1.1
	github.com/envoyproxy/go-control-plane v0.11.1
	github.com/prometheus/client_golang v1.17.0
	github.com/stretchr/testify v1.8.4
	github.com/gofiber/fiber/v2 v2.50.0
)
