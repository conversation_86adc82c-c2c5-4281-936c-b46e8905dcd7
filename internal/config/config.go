package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server    ServerConfig    `mapstructure:"server"`
	Discovery DiscoveryConfig `mapstructure:"discovery"`
	Envoy     EnvoyConfig     `mapstructure:"envoy"`
	DB        DBConfig        `mapstructure:"db"`
	Audit     AuditConfig     `mapstructure:"audit"`
	Cache     CacheConfig     `mapstructure:"cache"`
}

// ServerConfig contains server-related configuration
type ServerConfig struct {
	GRPCPort int `mapstructure:"grpc_port"`
	HTTPPort int `mapstructure:"http_port"`
}

// DiscoveryConfig contains cloud discovery configuration
type DiscoveryConfig struct {
	AWS   AWSConfig   `mapstructure:"aws"`
	GCP   GCPConfig   `mapstructure:"gcp"`
	Azure AzureConfig `mapstructure:"azure"`
}

// AWSConfig contains AWS-specific configuration
type AWSConfig struct {
	Regions []string `mapstructure:"regions"`
}

// GCPConfig contains GCP-specific configuration
type GCPConfig struct {
	Projects []string `mapstructure:"projects"`
}

// AzureConfig contains Azure-specific configuration
type AzureConfig struct {
	Subscriptions []string `mapstructure:"subs"`
}

// EnvoyConfig contains Envoy control plane configuration
type EnvoyConfig struct {
	XDSPort          int           `mapstructure:"xds_port"`
	SnapshotCacheTTL time.Duration `mapstructure:"snapshot_cache_ttl"`
}

// DBConfig contains database configuration
type DBConfig struct {
	Path string `mapstructure:"path"`
}

// AuditConfig contains audit configuration
type AuditConfig struct {
	Path string `mapstructure:"path"`
}

// CacheConfig contains cache configuration
type CacheConfig struct {
	Size int64 `mapstructure:"size"`
}

// Load loads configuration from the specified path
func Load(configPath string) (*Config, error) {
	v := viper.New()
	v.SetConfigFile(configPath)
	v.SetConfigType("yaml")

	// Set defaults
	setDefaults(v)

	// Read configuration file
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults(v *viper.Viper) {
	// Server defaults
	v.SetDefault("server.grpc_port", 8081)
	v.SetDefault("server.http_port", 8080)

	// Discovery defaults
	v.SetDefault("discovery.aws.regions", []string{"us-east-1", "us-west-2"})
	v.SetDefault("discovery.gcp.projects", []string{})
	v.SetDefault("discovery.azure.subs", []string{})

	// Envoy defaults
	v.SetDefault("envoy.xds_port", 18000)
	v.SetDefault("envoy.snapshot_cache_ttl", "5m")

	// Database defaults
	v.SetDefault("db.path", "/data/genji.db")

	// Audit defaults
	v.SetDefault("audit.path", "/data/immu")

	// Cache defaults
	v.SetDefault("cache.size", 134217728) // 128 MiB
}
