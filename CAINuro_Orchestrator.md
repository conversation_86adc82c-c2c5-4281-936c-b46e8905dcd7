CAINuro Orchestrator — Ultra‑Detailed Port & Rebrand Plan (v7)
Goal: Fork Lyft’s Clutch (https://github.com/lyft/clutch) and embed autoscaling, workflow execution, cross‑cloud resource discovery/search, dynamic Envoy control‑plane, persistence, and a Tailwind React 18 UI inside a single CGO‑free CAINuro static Go binary. All code is re‑namespaced to github.com/cainuro and visually aligned with CAINuro’s design system.

0 · Prerequisites & Naming
Key	Value
Upstream clone path	$HOME/src/clutch
New module path	$HOME/src/cainuro/orchestrator
Binary name	cainuro-orchestrator
License	Apache‑2.0 (retain upstream headers, add NOTICE)
Go build flags	CGO_ENABLED=0 -trimpath -ldflags "-s -w"
DB replacements	Postgres → Genji (embedded SQL/JSON) · Redis → Ristretto · Audit → ImmuDB
UI replacements	Material‑UI v4 → Tailwind v3 + Headless UI
New backend modules	service/search (AWS/GCP/Azure discovery) · service/envoycontrolplane (xDS)
1 · Repository Bootstrap (Day 1)
Sparse‑clone Clutch:

git clone --filter=blob:none --sparse https://github.com/lyft/clutch $HOME/src/clutch
cd $HOME/src/clutch
git sparse-checkout set backend frontend go.mod
Create CAINuro orchestrator module and React workspace:

mkdir -p $HOME/src/cainuro/orchestrator && cd $_
go mod init github.com/cainuro/orchestrator
pnpm create vite@latest frontend/orchestrator -- --template react-ts
Copy required directories:

Backend: backend/service (including search and envoycontrolplane), backend/module, backend/workflow, backend/api.

Frontend: frontend/workflows, frontend/packages/core, frontend/packages/components, frontend/packages/search.

Proto definitions → root/proto/orchestrator/v1.

2 · Automated Re‑Namespace (Day 2)
2.1 Rewrite Go import paths
find . -type f -name "*.go" | \
  xargs sed -i '' 's|github.com/lyft/clutch|github.com/cainuro/orchestrator|g'
2.2 Rewrite React/TypeScript package scopes
find frontend/orchestrator -type f -name "*.\(ts\|tsx\)" | \
  xargs sed -i '' 's|@clutch-sh|@cainuro|g'
2.3 Tidy module graphs
go mod tidy -e
pnpm install --filter orchestrator
3 · Database Layer Migration (Days 3 – 4)
3.1 Genji Adapter
Create internal/db/genjistore implementing Exec, Query, and Tx wrappers over genji.DB. Load schema from internal/db/schema.sql, which defines tables settings, workflows, users, and optional resource_cache for discovery result caching.

3.2 Ristretto Cache Wrapper
Replace all Redis calls with a singleton Ristretto instance (128 MiB default, 1‑minute TTL per key).

3.3 ImmuDB Audit Sink
Implement internal/audit/immu.Sink writing serialized audit entries to ImmuDB and exposing Verify(ctx, id) for tamper‑proof proofs.

4 · Dependency Injection with Google Wire (Days 5 – 6)
Remove Uber fx from copied code.

Add provider sets for SearchService, EnvoyControlPlaneService, GenjiStore, ImmuAuditSink, RistrettoCache.

Run wire ./... inside mage build.

5 · Config System using Viper (Day 6)
Create /etc/cainuro/orchestrator-config.yaml:

server:
  grpc_port: 8081
  http_port: 8080

discovery:
  aws:
    regions: [us-east-1, us-west-2]
  gcp:
    projects: [my-gcp-proj]
  azure:
    subs: [sub-123]

envoy:
  xds_port: 18000
  snapshot_cache_ttl: 5m

db:
  path: /data/genji.db

audit:
  path: /data/immu
cache:
  size: 134217728  # 128 MiB
Load with:

v := viper.New()
v.SetConfigName("orchestrator-config")
v.AddConfigPath("/etc/cainuro")
_ = v.ReadInConfig()
6 · Service, Gateway & Workflow Bindings (Days 7 – 10)
6.1 gRPC Service Registration
orchestrapb.RegisterAutoScalerServiceServer(grpcSrv, autoSvc)
orchestrapb.RegisterWorkflowServiceServer(grpcSrv, wfSvc)
orchestrapb.RegisterDiscoveryServiceServer(grpcSrv, discSvc)
orchestrapb.RegisterEnvoyControlPlaneServer(grpcSrv, envoySvc)
orchestrapb.RegisterDBAdminServiceServer(grpcSrv, dbSvc)
orchestrapb.RegisterAuditServiceServer(grpcSrv, auditSvc)
Expose REST/JSON via grpc-gateway at /v1/* routes.

6.2 Discovery Service Implementation
AWS: call resourcegroupstaggingapi.GetResources per region.

GCP: call AssetService.SearchAllResources per project.

Azure: call armresources.NewClient().ListBySubscription.

Normalize to proto Resource:

message Resource {
  string provider = 1; // aws|gcp|azure
  string type     = 2;
  string name     = 3;
  string id       = 4;
  map<string,string> tags = 5;
}
Store last‑run timestamp and optional metadata in resource_cache Genji table.

6.3 Workflow YAML Templates
root/workflows/cloud-search.yaml:

id: cloud-search
steps:
  - id: fetch
    type: discovery.search
    with:
      provider: "{{inputs.provider}}"
      query: "{{inputs.tag_query}}"
  - id: export
    type: workflow.export.csv
root/workflows/k8s-discover.yaml lists clusters, pods, and services.

6.4 Envoy Control‑Plane
Integrate Clutch’s snapshot cache logic.

Endpoint /v1/envoy/snapshot accepts JSON payload {tenant, clusters, routes}.

Store snapshot in Pebble at key /envoy/snapshots/{tenant}; push via xDS on change.

7 · UI Port & Tailwind Integration (Days 11 – 14)
7.1 Pages & Navigation
SearchDashboard – resource explorer with filters and tags.

DiscoveryWizard – multi‑step wizard to add credentials and launch discovery workflows.

EnvoyConfigEditor – Monaco editor with JSON schema validation and real‑time linter.

7.2 Key Components
Component	Implementation
ResourceTable	Headless UI table + virtual scroll (react‑window).
ProviderBadge	Tailwind classes bg-aws, bg-gcp, bg-azure.
CodeEditor	@monaco-editor/react, dark theme matching CAINuro palette.
Breadcrumbs	Tailwind flex & @heroicons/react icons.
7.3 API Integration
Add RTK query slice discoveryApi with endpoints searchResources and pushEnvoyConfig. Auto‑generated React hooks power UI components.

8 · Autoscaling Controller & Metrics (Day 15)
Export metric cainuro_discovery_jobs_total via Prometheus.

Autoscaling pods containing Discovery workers based on queue depth.

9 · UI an CI/CD Pipeline (Day 16)
Frontend Refactor with a Context‑Aware UI driven by React 18 + Redux Toolkit + SSE + ReactFlow

Design‑System Foundations

Token

Dark (Default)

Light

Description

--theme-bg-primary

#0e0f0f

#ffffff

Main body background

--theme-bg-secondary

#1b1b1e

#ffffff

Cards / raised surfaces

--theme-bg-sidebar

#0e0f0f

#edf2fa

Sidebar background

--theme-bg-container

#0e0f0f

#f9fbfd

App shell background

--theme-bg-chat

#1b1b1e

#ffffff

Chat transcript surface

--theme-bg-chat-input

#27282a

#eaeaea

Chat composer background

--theme-text-primary

#ffffff

#0e0f0f

Primary text

--theme-text-secondary

rgba(255,255,255,.6)

#7a7d7e

Secondary text

:root {
  /* dark */
  --theme-bg-primary:#0e0f0f;
  --theme-bg-secondary:#1b1b1e;
  --theme-bg-sidebar:#0e0f0f;
  --theme-bg-container:#0e0f0f;
  --theme-bg-chat:#1b1b1e;
  --theme-bg-chat-input:#27282a;
  --theme-text-primary:#ffffff;
  --theme-text-secondary:rgba(255,255,255,.6);
}
[data-theme="light"] {
  --theme-bg-primary:#ffffff;
  --theme-bg-secondary:#ffffff;
  --theme-bg-sidebar:#edf2fa;
  --theme-bg-container:#f9fbfd;
  --theme-bg-chat:#ffffff;
  --theme-bg-chat-input:#eaeaea;
  --theme-text-primary:#0e0f0f;
  --theme-text-secondary:#7a7d7e;
}

Semantic palette (Tailwind extension)

// tailwind.config.js
module.exports = {
  theme:{
    extend:{
      colors:{
        accent:'#3D4147',
        royalblue:'#065986',
        purple:'#4A1FB8',
        magenta:'#9E165F',
        danger:'#F04438',
        warn:'#854708',
        success:'#05603A',
        teal:'#0BA5EC',
      },
    },
  },
  darkMode:'class',
};

Typography

Base font: Inter (16px/1.5)

Weights: 400,500,600,700

Headings H1–H6 are fluid‑scaled via clamp().

Spacing & Radius

Scale (rem): 0, 0.25, 0.5, 0.75, 1, 1.5, 2, 3, 4, 6, 8, 12, 16.

Radius: sm 0.25rem, base 0.375rem, lg 0.5rem, full 9999px.

1 · Frontend App Shell (frontend/src/App.tsx)

export default function App() {
  const isMobile = useMediaQuery('(max-width:767px)');
  const { user } = useAuth();
  return (
    <div className="w-screen h-screen overflow-hidden flex bg-theme-bg-container">
      {!isMobile ? <Sidebar/> : <SidebarMobileHeader/>}
      {user && user.role!=='admin' ? <WorkspaceRouter/> : <Home/>}
    </div>
  );
}

All routing handled by React‑Router v6 nested layout → every child knows workspaceId & agentId via useAgentContext().

2 · Context‑Aware Patterns

2.1 Redux Toolkit Slices

agentSlice – active agent id, role, protobuf reflection JSON.

themeSlice – 'dark' | 'light' | 'system'.

streamSlice – SSE buffer (append/clear).

flowSlice – ReactFlow nodes & edges.

2.2 Local Compound Components

Private Select/Option etc. use localized React Context to avoid global churn.

2.3 Structural Context Hooks

useIsInModal() – returns boolean from ModalContext.

useMediaQuery() – modern matchMedia hook.

2.4 Live Signals

useSSE(url:string) – opens EventSource, dispatches JSON frames to streamSlice.append.

3 · ReactFlow Integration

3.1 Installation

pnpm add reactflow dagre

3.2 Core Component (components/Flow/FullAgentFlow.tsx)

Uses Dagre auto‑layout, dynamic node styling, SSE‑driven updates, and adapts zoom/fitView when rendered inside a modal.

3.3 Flow Slice & Redux Sync

interface FlowState { nodes:Node[]; edges:Edge[] }

Reducers: setNodes,setEdges,addNode,removeNode,addEdge,removeEdge.

4 · Component Library

shadcn/ui (Radix Primitives + Tailwind)

@heroicons/react for icons

@monaco-editor/react for Envoy JSON editor

Button variants, inputs, tables, cards – see concrete Tailwind class lists in user prompt (retained).

5 · Advanced Panels

AgentFlow – visual pipeline, SSE updates.

TokenStreamFlow – context‑window live tokens.

LogPanel – SSE logs with filters.

MetricTree (future) – D3 collapsible hierarchy lazy‑loaded.

6 · CAINuro Orchestrator Static‑Binary Fork

All days/timeline, namespace rewrite, Genji + Ristretto + ImmuDB migration, Google Wire DI, Viper config, discovery adapters, Envoy xDS, Tailwind UI rebuild, CI, testing, release artifacts are preserved exactly from the v7 plan.

Folder rules: All proto files & generated code live in /proto at repo root.

CGO disabled; target binary ≤ 32 MB.

Risks & Mitigations

Risk

Mitigation

Cloud SDK size ↗︎

Build tags to conditionally include AWS/GCP/Azure SDKs

Credential leakage

Accept STS/OAuth tokens only; never persist raw keys

Envoy version drift

Pin Envoy ≥ v1.29 & validate in CI

Tailwind purge false‑negatives

Go: lint, vet, unit + race, wire generation, SBOM via Syft.

React: pnpm lint && pnpm test && pnpm build.

Envoy JSON: validate snapshot using envoyvalidate GitHub Action.

10 · Testing & Validation (Days 17 – 19)
Layer	Tool	Success Criteria
Discovery Service	go test with mocked SDKs	> 0 resources fetched
Envoy xDS	Docker Envoy container	Snapshot accepted, traffic flowing
UI e2e	Playwright	Wizard creates creds → discovery results table populated
Audit Proof	immudb.Verify	proof valid for a random entry
Performance	hey 100k RPS	p99 < 12 ms gateway latency
11 · Release Artifacts
Asset	Path	Expected Size
Static binary	dist/cai-orchestrator-linux-amd64	≤ 32 MB
Embedded UI bundle	dist/admin-static	≈ 5 MB gzipped
Workflow YAMLs	root/workflows/	10 KB total
SBOM	dist/orchestrator.spdx.json	15 KB
12 · Detailed Gantt Timeline
Day	Deliverable
1	Bootstrap repo, copy backend & UI code
2	Automated re‑namespace for Go + React
3	Implement Genji adapter and schema
4	Integrate Ristretto cache & ImmuDB audit
5	Replace Uber fx with Google Wire DI
6	Viper config loader & YAML sample
7	Register gRPC core services (AutoScaler, Workflow, DB, Audit)
8	Build AWS/GCP/Azure discovery adapters
9	Build Envoy control‑plane backend, xDS endpoint
10	Author workflow YAMLs (cloud-search, k8s-discover)
11	Tailwind setup, recreate atomic UI components
12	Create SearchDashboard page & hooks
13	Create DiscoveryWizard & credential flow
14	Create EnvoyConfigEditor page & live linting
15	Add autoscaler metrics, Prometheus export
16	CI/CD pipelines (Go, React, Envoy validate)
17	Unit tests (Go + React) & integration tests
18	End‑to‑end Envoy xDS validation
19	Produce release artifacts & SBOM
Total	19 days
13 · Risks & Mitigations
Risk	Mitigation
Cloud SDK size inflates binary	Use Go build tags (// +build aws) to include SDKs only when env vars present
Credential leakage in discovery	Accept only STS/OAuth tokens; never persist creds in Genji
Envoy xDS version mismatch	Pin Envoy ≥ v1.29; run CI test with same version
API rate limits during discovery	Implement parallelism cap & exponential back‑off
Tailwind purge removing needed classes	Safelist important patterns in tailwind.config.js
14 · Success Criteria
Binary footprint ≤ 32 MB; file shows not stripped symbols removed.

Cold‑boot latency ≤ 350 ms on Mac M3; verified by hyperfine.

Discovery search returns resources from AWS, GCP, Azure in under 60 s for 1,000‑resource account.

Envoy snapshot push reaches connected Envoy instance and routes traffic without restart.

UI first contentful paint ≤ 1.2 s (Lighthouse mobile).

All tests green: go test ./..., vitest, Playwright.

Trivy scan: zero HIGH/CRITICAL CVEs in static binary.

Final deliverable: A single static CAINuro binary bundling Fiber gateway, HANN store, Genji, ImmuDB, cross‑cloud discovery, Envoy xDS, autoscaler, workflows, and an embedded Tailwind UI—ready to deploy from edge to Kubernetes.